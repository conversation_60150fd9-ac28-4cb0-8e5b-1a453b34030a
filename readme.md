# AdGuard 规则维护指南

本文档旨在为维护 `purepage-adguard.txt` 文件提供一套统一的标准和原则，以确保规则列表的整洁、高效和可维护性。所有后续的规则添加和修改都应遵循此指南。

## 核心原则

### 1. 注释规范

- **分组注释**：所有规则必须按其适用的主域名进行分组。每个分组前必须有一行格式为 `# Group: domain.com` 的注释。
- **时间戳**：文件开头必须保留一行格式为 `# 规则更新时间：YYYY-MM-DD` 的时间戳，并在每次更新后修改为当前日期。

### 2. 分组与排序

- **分组排序**：所有 `# Group` 按域名（例如 `163.com`, `baidu.com`）进行字母顺序升序排列。
- **组内排序**：每个分组内的规则，也应按字母顺序（从 a 到 z）进行升序排列。

### 3. 规则优化

- **删除重复**：在添加新规则或整理时，必须移除任何完全相同的重复规则。
- **合并相似规则**：如果多条规则是为隐藏同一父元素下的不同子元素，应尝试合并为一条更通用的父元素隐藏规则。例如，与其使用 `weibo.com##.sidebar > .item1` 和 `weibo.com##.sidebar > .item2`，不如直接使用 `weibo.com##.sidebar`，前提是不会误伤需要保留的元素。
- **优化选择器层级**：当多条规则针对同一类型元素但使用不同的 `:nth-child()` 选择器时，应考虑使用更通用的父元素选择器。例如，将 `div.fc-ab-root:nth-child(10) > div.fc-dialog-container` 和 `div.fc-ab-root:nth-child(12) > div.fc-dialog-container` 合并为 `div.fc-ab-root > div.fc-dialog-container`。这样可以覆盖所有可能的广告位置变化。注意：AdGuard不支持在 `:nth-child()` 中使用通配符 `*`。
- **修复语法错误**：确保所有规则都符合 AdGuard 的标准语法，避免因多余字符、空格或错误语法导致规则失效。
- **保留更精确的规则**：当两条规则功能相似但有细微差别时，应保留覆盖范围更精确、更不容易产生副作用的版本。

## 操作流程

1.  **添加新规则**：将新规则添加到对应的 `# Group` 下方。如果该域名分组不存在，请创建新的分组注释。
2.  **验证规则语法**：使用提供的验证脚本检查规则语法：`npm run validate` 或 `node validate-rules.js`
3.  **自动格式化**：添加完毕后，执行自动化脚本（或要求 AI）根据上述排序和优化原则重新整理整个文件。
4.  **更新时间戳**：在提交最终更改前，务必将文件顶部的更新日期修改为当天。

## 规则验证

本项目包含一个自动验证脚本 `validate-rules.js`，可以在提交前验证规则的语法正确性：

### 使用方法：
```bash
# 安装依赖（如果需要）
npm install

# 运行验证
npm run validate
# 或直接运行
node validate-rules.js
```

### 验证内容：
- CSS选择器语法检查
- 域名格式验证
- 括号匹配检查
- AdGuard特定语法限制检查
- 优化建议提示

### 实际测试方法：
1. **AdGuard浏览器扩展**：安装扩展，添加自定义规则，查看过滤日志
2. **AdGuard Home**：使用内置的规则检查功能
3. **浏览器开发者工具**：使用 `document.querySelectorAll()` 测试CSS选择器
4. **在线测试工具**：访问 https://adblock-tester.com/ 等网站测试效果

## 示例

### 整理前：

```adblock
# 规则更新时间：2025-07-22

# Group: weibo.com
weibo.com###__sidebar > div.item1
weibo.com###__sidebar

# Group: baidu.com
baidu.com##.ad
baidu.com##.promo
```

### 整理后：

```adblock
# 规则更新时间：2025-07-23

# Group: baidu.com
baidu.com##.ad
baidu.com##.promo

# Group: weibo.com
weibo.com###__sidebar
```

**说明**：`weibo.com` 分组中的具体规则被更通用的 `weibo.com###__sidebar` 覆盖，因此被移除。同时，分组按照 `baidu.com`、`weibo.com` 的字母顺序进行了排序。

### 选择器优化示例：

#### 优化前：
```adblock
# Group: 423down.com
423down.com##div.fc-ab-root:nth-child(10) > div.fc-dialog-container
423down.com##div.fc-ab-root:nth-child(12) > div.fc-dialog-container
```

#### 优化后：
```adblock
# Group: 423down.com
423down.com##div.fc-ab-root > div.fc-dialog-container
```

**说明**：通过移除具体的 `:nth-child()` 选择器，使用更通用的父元素选择器，可以适配更宽范围的广告位置，避免因广告位置变化而失效。

### 其他优化方法：

#### 使用CSS公式：
```adblock
# 选择第3个及之后的所有元素
example.com##div.ad-container:nth-child(n+3)

# 选择所有偶数位置的元素
example.com##div.ad-container:nth-child(even)
```

#### 使用属性选择器：
```adblock
# 选择包含特定类名的元素
example.com##div[class*="ad-"]
```
